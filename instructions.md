# ODude Smart Contracts

A decentralized domain name system (DNS) built on Ethereum with upgradeable smart contracts. The system consists of three main contracts: Registry (NFT management), TLD (top-level domain management), and Resolver (name resolution).

## 🏗️ Architecture

### Contract Overview

The ODude system is built with three upgradeable smart contracts:

1. **Registry.sol** - Main NFT registry contract
   - Handles ERC721 NFT minting and management
   - Processes ETH and ERC20 payments
   - Manages metadata storage and URI updates
   - Orchestrates interactions with TLD and Resolver contracts

2. **TLD.sol** - Top-Level Domain management
   - Stores TLD information, pricing, and allowances
   - Manages ERC20 token associations for payments
   - Handles commission settings and subdomain validation
   - Controls subdomain minting permissions

3. **Resolver.sol** - Name resolution service
   - Resolves domain names to addresses
   - Manages reverse mapping (address to name)
   - Provides lookup and resolution capabilities
   - Maintains on-chain records

### Key Features

- **Upgradeable Contracts**: All contracts use OpenZeppelin's UUPS proxy pattern
- **Multi-Payment Support**: Accepts both ETH and ERC20 tokens
- **Commission System**: Configurable commission rates for TLD owners
- **Flexible Pricing**: TLD owners can set subdomain prices (free, paid, or locked)
- **Reverse Mapping**: Address-to-name resolution
- **Access Control**: Role-based permissions and ownership management

## 🚀 Quick Start

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Git

### Installation

1. **Clone and install dependencies:**
   ```bash
   git clone <repository-url>
   cd odude-smartcontracts
   npm install
   ```

2. **Compile contracts:**
   ```bash
   npm run compile
   ```

3. **Run tests:**
   ```bash
   npm test
   ```

### Local Development

1. **Start a local Hardhat node:**
   ```bash
   npm run node
   ```
   This starts a local blockchain at `http://localhost:8545` with 20 test accounts.

2. **Deploy contracts to local network:**
   ```bash
   npm run deploy:localhost
   ```

3. **Run tests with verbose output:**
   ```bash
   npm run test:verbose
   ```

## 📋 Available Scripts

| Script | Description |
|--------|-------------|
| `npm run compile` | Compile all smart contracts |
| `npm test` | Run the complete test suite |
| `npm run test:verbose` | Run tests with detailed output |
| `npm run node` | Start local Hardhat blockchain |
| `npm run deploy:localhost` | Deploy to local network |
| `npm run deploy:sepolia` | Deploy to Sepolia testnet |
| `npm run deploy:mainnet` | Deploy to Ethereum mainnet |
| `npm run clean` | Clean compilation artifacts |
| `npm run coverage` | Generate test coverage report |

## 🔧 Configuration

### Network Configuration

Update `hardhat.config.js` to add network configurations:

```javascript
module.exports = {
  networks: {
    sepolia: {
      url: "https://sepolia.infura.io/v3/YOUR_INFURA_KEY",
      accounts: ["YOUR_PRIVATE_KEY"]
    },
    mainnet: {
      url: "https://mainnet.infura.io/v3/YOUR_INFURA_KEY",
      accounts: ["YOUR_PRIVATE_KEY"]
    }
  }
};
```

### Environment Variables

Create a `.env` file for sensitive data:
```
INFURA_API_KEY=your_infura_key
PRIVATE_KEY=your_private_key
ETHERSCAN_API_KEY=your_etherscan_key
```

## 🎯 Usage Examples

### Registering a TLD

```javascript
// Connect to Registry contract
const registry = await ethers.getContractAt("Registry", registryAddress);

// Register a TLD (costs 1 ETH by default)
await registry.claim(
  1,                    // tokenId
  "mycompany",         // TLD name
  "https://meta.uri",  // metadata URI
  userAddress,         // recipient
  { value: ethers.parseEther("1.0") }
);
```

### Registering a Subdomain

```javascript
// Set subdomain price (by TLD owner)
const tld = await ethers.getContractAt("TLD", tldAddress);
await tld.setTLDPrice(1, ethers.parseEther("0.1")); // 0.1 ETH

// Register subdomain
await registry.claim(
  2,                      // tokenId
  "app@mycompany",       // subdomain@tld
  "https://app.meta.uri", // metadata URI
  userAddress,            // recipient
  { value: ethers.parseEther("0.1") }
);
```

### Setting Up ERC20 Payments

```javascript
// Associate ERC20 token with TLD
await tld.setTLDToken(1, tokenAddress);

// Set price in tokens
await tld.setTLDPrice(1, ethers.parseEther("100")); // 100 tokens

// User approves and registers
await erc20Token.approve(registryAddress, ethers.parseEther("100"));
await registry.claim(3, "service@mycompany", "uri", userAddress);
```

## 🧪 Testing

The test suite covers:

- **TLD Registration**: Basic minting, payment validation, duplicate prevention
- **Subdomain Registration**: ETH/ERC20 payments, TLD owner permissions, locked domains
- **Payment Processing**: Commission distribution, insufficient funds handling
- **Reverse Mapping**: Setting and retrieving address-to-name mappings
- **URI Management**: Token URI updates, locked subdomain restrictions
- **Administrative Functions**: Pausing, withdrawals, access control

Run specific test categories:
```bash
# Run all tests
npm test

# Run with gas reporting
REPORT_GAS=true npm test

# Run coverage analysis
npm run coverage
```

## 🔐 Security Considerations

### Upgradeability

All contracts use OpenZeppelin's UUPS proxy pattern:
- Only contract owners can authorize upgrades
- Implementation contracts are deployed separately
- Proxy contracts maintain state across upgrades

### Access Control

- **Registry**: Owner can pause/unpause, withdraw funds, set contract addresses
- **TLD**: Owner sets global config, TLD owners control their domains
- **Resolver**: Only Registry contract can update resolution records

### Payment Security

- Reentrancy protection on all payment functions
- Balance and allowance checks before ERC20 transfers
- Commission calculations prevent overflow/underflow

## 📁 Project Structure

```
odude-smartcontracts/
├── contracts/
│   ├── Registry.sol      # Main NFT registry
│   ├── TLD.sol          # TLD management
│   ├── Resolver.sol     # Name resolution
│   ├── helper.sol       # String utilities
│   ├── lib.sol          # Additional utilities
│   └── test/
│       └── MockERC20.sol # Test token
├── scripts/
│   └── deploy.js        # Deployment script
├── test/
│   └── Registry.test.js # Test suite
├── deployments/         # Deployment records
├── hardhat.config.js    # Hardhat configuration
├── package.json         # Dependencies and scripts
└── instructions.md      # This file
```

## 🚀 Deployment

### Local Deployment

1. Start local node: `npm run node`
2. Deploy contracts: `npm run deploy:localhost`
3. Contract addresses will be saved to `deployments/localhost-deployment.json`

### Testnet Deployment

1. Configure network in `hardhat.config.js`
2. Fund deployer account with testnet ETH
3. Deploy: `npm run deploy:sepolia`
4. Verify contracts on Etherscan (optional)

### Mainnet Deployment

1. **Security checklist:**
   - Audit all contracts
   - Test thoroughly on testnets
   - Verify deployer account security
   - Double-check configuration

2. Deploy: `npm run deploy:mainnet`
3. Verify contracts on Etherscan
4. Transfer ownership to multisig wallet

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Write tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For questions or support:
- Email: <EMAIL>
- GitHub Issues: [Create an issue](https://github.com/your-repo/issues)

---

**⚠️ Important**: This is experimental software. Use at your own risk and conduct thorough testing before mainnet deployment.
