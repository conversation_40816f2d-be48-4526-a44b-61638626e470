contracts/interfaces/ITLD.sol

contracts/interfaces/IResolver.sol

contracts/Registry.sol — the ERC-721 registry + mint/claim flow, interacts with ITLD + IResolver

contracts/TLD.sol — per-TLD settings (allow, comm, erc token). Authorised setters verify the token owner via the Registry.

contracts/Resolver.sol — reverse mapping and simple text records.

================================




# How to deploy (recommended order)

Deploy Registry proxy (initializer sets owner).

Deploy TLD proxy — pass registry address to its initialize.

Deploy Resolver proxy — pass registry address to its initialize.

Call registry.setTLD(addressOfTLD) and registry.setResolver(addressOfResolver) to wire them up.
(Every contract is upgradeable — later you can upgrade each implementation independently.)

================================

# Notes, caveats and next steps

# Preserved behaviour — 
The claim() flow in Registry.sol follows your original payment splitting between TLD owner and platform owner; now TLD-specific settings (allow, comm, erc) live in TLD.sol and are read by the registry during claim().

# Access control — 
Only the token owner (checked via Registry.ownerOf) can set TLD settings in TLD.sol or set text/reverse records in Resolver.sol.

# Helpers — 
Registry.sol still imports your helper.sol and lib.sol so Helper.split and ODudeUtils.toLower remain available. Keep those files intact.

# Events — 
I preserved EthPayment and ERC20Payment events and added some module-specific events.

# Upgradability — 
All contracts are UUPS upgradeable. Deploy each contract as an upgradeable proxy (e.g., via Hardhat + @openzeppelin/hardhat-upgrades) or manual ERC1967Proxy if you prefer Remix.

# Deployment order reminder:

- Deploy Registry proxy first (initializer with owner).

- Deploy TLD proxy, call initialize(registryAddress) or pass registry in initializer if you set it up. (I used a setter + initializer.)

- Deploy Resolver proxy, call initialize(registryAddress).

- On the registry, call setTLD(tldAddress) and setResolver(resolverAddress).

# Testing — 
start with a local Hardhat node, deploy these three proxies, then test flows:

- Mint a TLD (call claim() with title = gold), then as owner of TLD call setAllow, setComm, setERC.

- Mint a subdomain abc@gold via claim() and check payments.

- Test Resolver.setText and setReverse.