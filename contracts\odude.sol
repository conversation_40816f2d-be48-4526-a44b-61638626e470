// Enable optimizer
// SPDX-License-Identifier: MIT
// Compatible with OpenZeppelin Contracts ^5.0.0
pragma solidity ^0.8.20;

// OpenZeppelin upgradeable contracts for ERC721 functionality
import "@openzeppelin/contracts-upgradeable/token/ERC721/ERC721Upgradeable.sol";
import "@openzeppelin/contracts-upgradeable/token/ERC721/extensions/ERC721EnumerableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/token/ERC721/extensions/ERC721URIStorageUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/token/ERC721/extensions/ERC721PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

// Custom helpers
import "./helper.sol";
import "./lib.sol";

/// @custom:security-contact <EMAIL>
contract ODude is
    Initializable,
    ERC721Upgradeable,
    ERC721EnumerableUpgradeable,
    ERC721URIStorageUpgradeable,
    ERC721PausableUpgradeable,
    OwnableUpgradeable,
    UUPSUpgradeable
{
    /// @dev Metadata structure storing NFT-related data
    struct Metadata {
        uint256 id;
        string title;
        uint256 allow;
        uint256 comm;
        address erc;
    }

    // Attach custom utility methods for string
    using ODudeUtils for string;

    // Mappings to track metadata by id, title, and address
    mapping(uint256 => Metadata) id_to_date;
    mapping(string => Metadata) id_to_title;
    mapping(address => Metadata) id_to_addr;

    // Global config values for pricing and commissions
    uint256 private nft_price;
    uint256 private nft_comm_dealer;

    /// @dev Prevents initializer from being called twice
    constructor() {
        _disableInitializers();
    }

    /// @notice Initializes the contract and sets token name/symbol
    function initialize(address initialOwner) public initializer {
        __ERC721_init("ODude", "ODUDE");
        __ERC721Enumerable_init();
        __ERC721URIStorage_init();
        __ERC721Pausable_init();
        __Ownable_init(initialOwner);
        __UUPSUpgradeable_init();
    }

    /// @notice Pauses all token transfers
    function pause() public onlyOwner {
        _pause();
    }

    /// @notice Resumes token transfers
    function unpause() public onlyOwner {
        _unpause();
    }

    /// @dev Internal minting function restricted to contract owner
    function safeMint(
        address to,
        uint256 tokenId,
        string memory uri
    ) internal onlyOwner {
        _safeMint(to, tokenId);
        _setTokenURI(tokenId, uri);
    }

    /// @dev Authorization logic for contract upgrades
    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    /// @dev Override for update function required by multiple inheritance
    function _update(
        address to,
        uint256 tokenId,
        address auth
    )
        internal
        override(
            ERC721Upgradeable,
            ERC721EnumerableUpgradeable,
            ERC721PausableUpgradeable
        )
        returns (address)
    {
        return super._update(to, tokenId, auth);
    }

    /// @dev Override for balance update logic
    function _increaseBalance(
        address account,
        uint128 value
    ) internal override(ERC721Upgradeable, ERC721EnumerableUpgradeable) {
        super._increaseBalance(account, value);
    }

    /// @notice Returns token URI
    function tokenURI(
        uint256 tokenId
    )
        public
        view
        override(ERC721Upgradeable, ERC721URIStorageUpgradeable)
        returns (string memory)
    {
        return super.tokenURI(tokenId);
    }

    /// @notice Allows NFT owner to update their token URI
    function setTokenURI(
        uint256 tokenId,
        string memory tokenURIlink
    ) external payable {
        require(ownerOf(tokenId) == _msgSender(), "NOT_OWNER");

        string memory primary = subdomain(titleOf(tokenId), 2);
        if (keccak256(bytes(primary)) != keccak256(bytes(""))) {
            uint256 iid = getID(primary);
            require(getAllow(iid) != 1982, "URL_LOCKED");
        }

        _setTokenURI(tokenId, tokenURIlink);
        payable(owner()).transfer(msg.value);
    }

    /// @notice Check if the interface is supported
    function supportsInterface(
        bytes4 interfaceId
    )
        public
        view
        override(
            ERC721Upgradeable,
            ERC721EnumerableUpgradeable,
            ERC721URIStorageUpgradeable
        )
        returns (bool)
    {
        return super.supportsInterface(interfaceId);
    }

    /// @dev Splits a title at "@" and returns the left/right part
    function subdomain(
        string memory title,
        uint256 pos
    ) internal pure returns (string memory) {
        (string memory a, string memory b) = Helper.split(title, "@");
        if (pos == 1) {
            return a;
        } else {
            return b;
        }
    }

    /// @dev Checks whether tokenId has a valid Metadata entry
    function exists(uint256 tokenId) internal view returns (bool) {
        return id_to_date[tokenId].id != 0;
    }

    /// @dev Checks whether a title is already used
    function existsByTitle(string memory title) internal view returns (bool) {
        return id_to_title[title].id != 0;
    }

    /// @notice Sets the base price for minting TLDs
    function setNftPrice(uint256 _nft_price) public onlyOwner {
        nft_price = _nft_price;
    }

    /// @notice Gets the base minting price
    function getNftPrice() public view returns (uint256) {
        return nft_price;
    }

    /// @notice Sets the global dealer commission value
    function set_Comm_Dealer(uint256 _nft_comm_dealer) public onlyOwner {
        nft_comm_dealer = _nft_comm_dealer;
    }

    /// @notice Gets the dealer commission value
    function getComm_Dealer() public view returns (uint256) {
        return nft_comm_dealer;
    }

    /// @notice Sets token-specific commission value
    function set_Comm_TLD(uint256 tokenId, uint256 _comm) public onlyOwner {
        require(exists(tokenId), "NAME_404");
        require(_comm > 0, "COMM_GT0");
        require(_comm < 100, "COMM_LT100");
         // Check that the commission is an integer (no decimal places)

        require(_comm == uint256(_comm), "Commission cannot have decimal places");

        id_to_date[tokenId].comm = _comm;
    }

    /// @notice Retrieves token-specific commission value
    function get_Comm_TLD(uint256 tokenId) public view returns (uint256) {
        require(exists(tokenId), "NAME_404");
        return id_to_date[tokenId].comm;
    }

    /// @notice Associates an ERC20 token address with a specific token ID
    function set_erc_TLD(
        uint256 tokenId,
        address _erc20TokenAddress
    ) external payable {
        require(exists(tokenId), "NAME_404");
        require(ownerOf(tokenId) == _msgSender(), "NOT_OWNER");

        if (_erc20TokenAddress == address(0)) {
            delete id_to_date[tokenId].erc;
        } else {
            id_to_date[tokenId].erc = _erc20TokenAddress;
        }
    }

    /// @notice Gets the ERC20 token address set for the given token
    function get_erc_TLD(uint256 tokenId) public view returns (address) {
        require(exists(tokenId), "NAME_404");
        return id_to_date[tokenId].erc;
    }

    /// @dev Internal mint function called by claim(). Sets metadata and mints NFT
    function mint(
        uint256 _id,
        string memory _title,
        string memory _tokenUri,
        address to
    ) internal {
        require(!existsByTitle(_title), "NAME_TAKEN");
        uint256 _allow = 1 ether;
        uint256 _comm = getComm_Dealer();

        id_to_date[_id] = Metadata(_id, _title, _allow, _comm, address(0));
        id_to_title[_title] = Metadata(_id, _title, _allow, _comm, address(0));

        _safeMint(to, _id);
        _setTokenURI(_id, _tokenUri);
    }


    // Events for payments and TLD actions
    event EthPayment(address indexed payer, uint256 tldOwnerAmount, uint256 platformAmount);
    event ERC20Payment(address indexed payer, address indexed token, uint256 tldOwnerAmount, uint256 platformAmount);
    event TLDPriceSet(uint256 indexed tokenId, uint256 price);
    event TLDCommissionSet(uint256 indexed tokenId, uint256 commission);
    event TLDAllowanceSet(uint256 indexed tokenId, uint256 allowance);
    event ERC20TokenSet(uint256 indexed tokenId, address indexed token);

/// @notice Claims a TLD or subdomain NFT, handling ETH/ERC20 payments and commissions
function claim(
    uint256 tokenId,
    string calldata title,
    string memory _tokenUri,
    address _to
) external payable {
    string memory primary = subdomain(ODudeUtils.toLower(title), 2);
    uint256 level;
    uint256 listingPrice;

    // Check if title is TLD or subdomain
    if (keccak256(bytes(primary)) != keccak256(bytes(""))) {
        // It is a subdomain. Get ID of parent TLD
        uint256 tld_id = getID(primary);
        level = getAllow(tld_id); // Price set by TLD owner

        // Only TLD owner can mint subdomain for free or locked
        if (level == 0 || level == 1982) {
            require(msg.sender == getOwner(tld_id), "NOT_TLD_OWNER");
            payable(owner()).transfer(msg.value);
        } else {
            // Get ERC20 token contract address associated with the TLD
            address erc20TokenAddress = get_erc_TLD(tld_id);

            // Calculate distribution amounts
            uint256 tokenAmountToOwner = (level * get_Comm_TLD(tld_id)) / 100;
            uint256 remainingTokenAmount = level - tokenAmountToOwner;

            if (erc20TokenAddress != address(0)) {
                // If ERC20 is set, use token-based payment
                listingPrice = level;

                // Check sender's balance
                uint256 senderBalance = IERC20(erc20TokenAddress).balanceOf(msg.sender);
                require(senderBalance >= listingPrice, "BAL_LOW");

                // Check sender's allowance
                uint256 allowance = IERC20(erc20TokenAddress).allowance(msg.sender, address(this));
                require(allowance >= listingPrice, "ALLOW_LOW");

                // Transfer full token amount once to this contract
                require(
                    IERC20(erc20TokenAddress).transferFrom(msg.sender, address(this), listingPrice),
                    "TRANSFER_FAIL"
                );

                // Split between TLD owner and platform
                require(
                    IERC20(erc20TokenAddress).transfer(getOwner(tld_id), tokenAmountToOwner),
                    "TLD_PAY_FAIL"
                );
                require(
                    IERC20(erc20TokenAddress).transfer(owner(), remainingTokenAmount),
                    "OWNR_PAY_FAIL"
                );

                emit ERC20Payment(msg.sender, erc20TokenAddress, tokenAmountToOwner, remainingTokenAmount);
            } else {
                // Use ETH payment
                require(msg.value >= level, "PRICE_MISMATCH");

                // Distribute ETH between TLD owner and contract owner
                payable(getOwner(tld_id)).transfer(tokenAmountToOwner);
                payable(owner()).transfer(remainingTokenAmount);

                emit EthPayment(msg.sender, tokenAmountToOwner, remainingTokenAmount);
            }
        }
    } else {
        // It is a TLD
        level = getNftPrice();
        listingPrice = level * 1 wei;

        require(msg.value >= listingPrice, "PRICE_MISMATCH");
        payable(owner()).transfer(msg.value);

        emit EthPayment(msg.sender, 0, msg.value); // All goes to platform
    }

    // Validations before minting
    require(tokenId > 0, "ID_ZERO");
    require(bytes(title).length > 0, "NAME_EMPTY");
    require(!exists(tokenId), "ID_EXISTS");

    // Mint NFT
    mint(tokenId, ODudeUtils.toLower(title), _tokenUri, _to);
}



    /// @notice Get the title associated with a tokenId
    function titleOf(uint256 tokenId) public view returns (string memory) {
        require(exists(tokenId), "NAME_404");
        return id_to_date[tokenId].title;
    }

    /// @notice Get tokenId by title
    function getID(string memory title) public view returns (uint256) {
        require(exists(id_to_title[ODudeUtils.toLower(title)].id), "NAME_404");
        return id_to_title[ODudeUtils.toLower(title)].id;
    }

    /// @notice Get the owner address of a tokenId
    function getOwner(uint256 tokenId) public view returns (address) {
        return ownerOf(tokenId);
    }

    /// @notice Get the owner address by title
    function getOwnerByName(string memory _name) public view returns (address) {
        return ownerOf(getID(_name));
    }

    /// @notice Returns current ETH balance of the contract
    function getBalance() public view returns (uint256) {
        return address(this).balance;
    }

    /// @notice Sets subdomain minting allowance for a TLD token
    function setAllow(uint256 tokenId, uint256 _allow) external payable {
        require(exists(tokenId), "NAME_404");
        require(ownerOf(tokenId) == _msgSender(), "NOT_OWNER");
        id_to_date[tokenId].allow = _allow;
    }

    /// @notice Get subdomain minting allowance for a token
    function getAllow(uint256 tokenId) public view returns (uint256) {
        require(exists(tokenId), "NAME_404");
        return id_to_date[tokenId].allow;
    }

    /// @notice Withdraws all ETH from the contract to the owner
    function withdraw() public onlyOwner {
        payable(owner()).transfer(address(this).balance);
    }

    /// @notice Withdraws ERC20 tokens held by the contract
    function withdrawERC20(address tokenAddress, uint256 amount) public onlyOwner {
        require(tokenAddress != address(0), "Invalid token address");
        require(amount > 0, "Invalid amount");

        IERC20 token = IERC20(tokenAddress);
        uint256 balance = token.balanceOf(address(this));
        require(balance >= amount, "Insufficient balance");
        require(token.transfer(owner(), amount), "Token transfer failed");
    }

    /// @notice Sets reverse mapping (address => name) for caller
    function setReverse(uint256 tokenId) external payable {
        require(exists(tokenId), "NAME_404");
        require(ownerOf(tokenId) == _msgSender(), "NOT_OWNER");
        id_to_addr[msg.sender].title = titleOf(tokenId);
    }

    /// @notice Gets reverse mapped title of an address
    function getReverse(address _addr) public view returns (string memory) {
        return id_to_addr[_addr].title;
    }

/// @notice Accepts plain ETH transfers
    receive() external payable {}

    /// @notice Accepts unknown calls with data or ETH
fallback() external payable {}

}