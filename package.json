{"name": "odude-smartcontracts", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"compile": "npx hardhat compile", "test": "npx hardhat test", "test:verbose": "npx hardhat test --verbose", "node": "npx hardhat node", "deploy:localhost": "npx hardhat run scripts/deploy.js --network localhost", "deploy:sepolia": "npx hardhat run scripts/deploy.js --network sepolia", "deploy:mainnet": "npx hardhat run scripts/deploy.js --network mainnet", "clean": "npx hardhat clean", "size": "npx hardhat size-contracts", "coverage": "npx hardhat coverage"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^6.1.0", "@openzeppelin/contracts": "^5.4.0", "@openzeppelin/contracts-upgradeable": "^5.4.0", "@openzeppelin/hardhat-upgrades": "^3.9.1", "dotenv": "^16.3.1", "hardhat": "^2.26.3"}}