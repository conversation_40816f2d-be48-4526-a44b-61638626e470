const { expect } = require("chai");
const { ethers, upgrades } = require("hardhat");

describe("ODude Registry System", function () {
  let registry, tld, resolver;
  let owner, user1, user2, user3;
  let mockERC20;

  beforeEach(async function () {
    // Get signers
    [owner, user1, user2, user3] = await ethers.getSigners();

    // Deploy TLD contract
    const TLD = await ethers.getContractFactory("TLD");
    tld = await upgrades.deployProxy(TLD, [owner.address], {
      initializer: "initialize",
      kind: "uups"
    });
    await tld.waitForDeployment();

    // Deploy Resolver contract
    const Resolver = await ethers.getContractFactory("Resolver");
    resolver = await upgrades.deployProxy(Resolver, [owner.address], {
      initializer: "initialize",
      kind: "uups"
    });
    await resolver.waitForDeployment();

    // Deploy Registry contract
    const Registry = await ethers.getContractFactory("Registry");
    registry = await upgrades.deployProxy(Registry, [owner.address], {
      initializer: "initialize",
      kind: "uups"
    });
    await registry.waitForDeployment();

    // Configure contract relationships
    await registry.setContracts(await tld.getAddress(), await resolver.getAddress());
    await resolver.setRegistryContract(await registry.getAddress());
    await tld.setRegistryContract(await registry.getAddress());

    // Set initial configuration
    await tld.setBaseTLDPrice(ethers.parseEther("1.0"));
    await tld.setDefaultCommission(10);

    // Deploy mock ERC20 token for testing
    const MockERC20 = await ethers.getContractFactory("contracts/test/MockERC20.sol:MockERC20");
    mockERC20 = await MockERC20.deploy("Test Token", "TEST", ethers.parseEther("1000000"));
    await mockERC20.waitForDeployment();

    // Give users some test tokens
    await mockERC20.transfer(user1.address, ethers.parseEther("1000"));
    await mockERC20.transfer(user2.address, ethers.parseEther("1000"));
  });

  describe("TLD Registration", function () {
    it("Should allow TLD registration with correct payment", async function () {
      const tokenId = 1;
      const tldName = "test";
      const tokenUri = "https://example.com/1";

      await expect(
        registry.connect(user1).claim(tokenId, tldName, tokenUri, user1.address, {
          value: ethers.parseEther("1.0")
        })
      ).to.emit(registry, "TLDMinted")
        .withArgs(tokenId, tldName, user1.address);

      // Verify NFT was minted
      expect(await registry.ownerOf(tokenId)).to.equal(user1.address);
      expect(await registry.nameOf(tokenId)).to.equal(tldName);
      expect(await registry.tokenURI(tokenId)).to.equal(tokenUri);

      // Verify TLD was registered
      const tldInfo = await tld.getTLD(tokenId);
      expect(tldInfo.name).to.equal(tldName);
      expect(tldInfo.owner).to.equal(user1.address);

      // Verify resolver record
      expect(await resolver.resolve(tldName)).to.equal(user1.address);
    });

    it("Should reject TLD registration with insufficient payment", async function () {
      await expect(
        registry.connect(user1).claim(1, "test", "uri", user1.address, {
          value: ethers.parseEther("0.5")
        })
      ).to.be.revertedWith("Registry: Insufficient payment");
    });

    it("Should reject duplicate TLD names", async function () {
      await registry.connect(user1).claim(1, "test", "uri", user1.address, {
        value: ethers.parseEther("1.0")
      });

      await expect(
        registry.connect(user2).claim(2, "test", "uri", user2.address, {
          value: ethers.parseEther("1.0")
        })
      ).to.be.revertedWith("Registry: Name taken");
    });
  });

  describe("Subdomain Registration", function () {
    beforeEach(async function () {
      // Register a TLD first
      await registry.connect(user1).claim(1, "test", "uri", user1.address, {
        value: ethers.parseEther("1.0")
      });
    });

    it("Should allow subdomain registration with ETH payment", async function () {
      // Set subdomain price
      await tld.connect(user1).setTLDPrice(1, ethers.parseEther("0.1"));

      const tokenId = 2;
      const subdomainName = "sub@test";
      const tokenUri = "https://example.com/2";

      await expect(
        registry.connect(user2).claim(tokenId, subdomainName, tokenUri, user2.address, {
          value: ethers.parseEther("0.1")
        })
      ).to.emit(registry, "SubdomainMinted")
        .withArgs(tokenId, subdomainName, 1, user2.address);

      // Verify NFT was minted
      expect(await registry.ownerOf(tokenId)).to.equal(user2.address);
      expect(await registry.nameOf(tokenId)).to.equal(subdomainName);

      // Verify resolver record
      expect(await resolver.resolve(subdomainName)).to.equal(user2.address);
    });

    it("Should allow free subdomain minting by TLD owner", async function () {
      // Set subdomain price to 0 (free)
      await tld.connect(user1).setTLDPrice(1, 0);

      await expect(
        registry.connect(user1).claim(2, "sub@test", "uri", user2.address)
      ).to.emit(registry, "SubdomainMinted");

      expect(await registry.ownerOf(2)).to.equal(user2.address);
    });

    it("Should reject free subdomain minting by non-TLD owner", async function () {
      // Set subdomain price to 0 (free)
      await tld.connect(user1).setTLDPrice(1, 0);

      await expect(
        registry.connect(user2).claim(2, "sub@test", "uri", user2.address)
      ).to.be.revertedWith("Registry: Not TLD owner");
    });

    it("Should handle locked subdomains", async function () {
      // Set subdomain price to 1982 (locked)
      await tld.connect(user1).setTLDPrice(1, 1982);

      // Only TLD owner can mint
      await expect(
        registry.connect(user1).claim(2, "sub@test", "uri", user2.address)
      ).to.emit(registry, "SubdomainMinted");

      // Non-TLD owner cannot mint
      await expect(
        registry.connect(user2).claim(3, "sub2@test", "uri", user2.address)
      ).to.be.revertedWith("Registry: Not TLD owner");
    });
  });

  describe("ERC20 Payments", function () {
    beforeEach(async function () {
      // Register a TLD
      await registry.connect(user1).claim(1, "test", "uri", user1.address, {
        value: ethers.parseEther("1.0")
      });

      // Set ERC20 token for the TLD
      await tld.connect(user1).setTLDToken(1, await mockERC20.getAddress());
      
      // Set subdomain price
      await tld.connect(user1).setTLDPrice(1, ethers.parseEther("10")); // 10 tokens
    });

    it("Should allow subdomain registration with ERC20 payment", async function () {
      // Approve tokens
      await mockERC20.connect(user2).approve(await registry.getAddress(), ethers.parseEther("10"));

      await expect(
        registry.connect(user2).claim(2, "sub@test", "uri", user2.address)
      ).to.emit(registry, "ERC20Payment");

      expect(await registry.ownerOf(2)).to.equal(user2.address);
    });

    it("Should reject ERC20 payment with insufficient balance", async function () {
      // Transfer away user's tokens
      await mockERC20.connect(user2).transfer(user3.address, ethers.parseEther("1000"));

      await expect(
        registry.connect(user2).claim(2, "sub@test", "uri", user2.address)
      ).to.be.revertedWith("Registry: Insufficient balance");
    });

    it("Should reject ERC20 payment with insufficient allowance", async function () {
      // Don't approve tokens
      await expect(
        registry.connect(user2).claim(2, "sub@test", "uri", user2.address)
      ).to.be.revertedWith("Registry: Insufficient allowance");
    });
  });

  describe("Reverse Mapping", function () {
    beforeEach(async function () {
      // Register a TLD
      await registry.connect(user1).claim(1, "test", "uri", user1.address, {
        value: ethers.parseEther("1.0")
      });
    });

    it("Should allow setting reverse mapping", async function () {
      await registry.connect(user1).setReverse(1);
      expect(await resolver.reverse(user1.address)).to.equal("test");
    });

    it("Should reject reverse mapping by non-owner", async function () {
      await expect(
        registry.connect(user2).setReverse(1)
      ).to.be.revertedWith("Registry: Not owner");
    });
  });

  describe("URI Management", function () {
    beforeEach(async function () {
      // Register a TLD
      await registry.connect(user1).claim(1, "test", "uri", user1.address, {
        value: ethers.parseEther("1.0")
      });
    });

    it("Should allow token URI updates by owner", async function () {
      const newUri = "https://newuri.com/1";
      await registry.connect(user1).setTokenURI(1, newUri);
      expect(await registry.tokenURI(1)).to.equal(newUri);
    });

    it("Should reject URI updates by non-owner", async function () {
      await expect(
        registry.connect(user2).setTokenURI(1, "newuri")
      ).to.be.revertedWith("Registry: Not owner");
    });

    it("Should reject URI updates for locked subdomains", async function () {
      // Set subdomain price to 1982 (locked)
      await tld.connect(user1).setTLDPrice(1, 1982);
      
      // Mint a subdomain
      await registry.connect(user1).claim(2, "sub@test", "uri", user2.address);

      // Try to update URI - should fail
      await expect(
        registry.connect(user2).setTokenURI(2, "newuri")
      ).to.be.revertedWith("Registry: URI locked");
    });
  });

  describe("Administrative Functions", function () {
    it("Should allow owner to pause and unpause", async function () {
      await registry.pause();
      
      await expect(
        registry.connect(user1).claim(1, "test", "uri", user1.address, {
          value: ethers.parseEther("1.0")
        })
      ).to.be.reverted;

      await registry.unpause();
      
      await expect(
        registry.connect(user1).claim(1, "test", "uri", user1.address, {
          value: ethers.parseEther("1.0")
        })
      ).to.emit(registry, "TLDMinted");
    });

    it("Should allow owner to withdraw ETH", async function () {
      // Send ETH directly to contract
      await user1.sendTransaction({
        to: await registry.getAddress(),
        value: ethers.parseEther("1.0")
      });

      const contractBalance = await registry.getBalance();
      expect(contractBalance).to.equal(ethers.parseEther("1.0"));

      const initialOwnerBalance = await ethers.provider.getBalance(owner.address);
      await registry.withdraw();
      const finalOwnerBalance = await ethers.provider.getBalance(owner.address);

      const finalContractBalance = await registry.getBalance();
      expect(finalContractBalance).to.equal(0);
      expect(finalOwnerBalance).to.be.gt(initialOwnerBalance);
    });
  });
});
