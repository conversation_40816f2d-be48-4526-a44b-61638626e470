// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";

/// @title TLD - Top-Level Domain Management Contract
/// @notice Manages TLD information, pricing, allowances, and ERC20 token associations
/// @custom:security-contact <EMAIL>
contract TLD is 
    Initializable,
    OwnableUpgradeable,
    UUPSUpgradeable,
    ReentrancyGuardUpgradeable
{
    /// @dev TLD metadata structure
    struct TLDInfo {
        uint256 id;
        string name;
        uint256 price;           // Subdomain minting price (0 = free, 1982 = locked)
        uint256 commission;      // Commission percentage for TLD owner
        address erc20Token;      // Associated ERC20 token for payments
        address owner;           // TLD owner address
        bool exists;             // Whether TLD exists
    }

    // Mappings for TLD data
    mapping(uint256 => TLDInfo) private _tldById;
    mapping(string => uint256) private _tldIdByName;
    mapping(address => uint256[]) private _tldsByOwner;

    // Global configuration
    uint256 private _baseTLDPrice;
    uint256 private _defaultCommission;

    // Registry contract address (authorized to register TLDs)
    address private _registryContract;

    // Events
    event TLDRegistered(uint256 indexed id, string indexed name, address indexed owner);
    event TLDPriceSet(uint256 indexed id, uint256 price);
    event TLDCommissionSet(uint256 indexed id, uint256 commission);
    event TLDAllowanceSet(uint256 indexed id, uint256 allowance);
    event ERC20TokenSet(uint256 indexed id, address indexed token);
    event TLDOwnershipTransferred(uint256 indexed id, address indexed previousOwner, address indexed newOwner);

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    /// @notice Initializes the TLD contract
    /// @param initialOwner The initial owner of the contract
    function initialize(address initialOwner) public initializer {
        __Ownable_init(initialOwner);
        __UUPSUpgradeable_init();
        __ReentrancyGuard_init();
        
        _baseTLDPrice = 1 ether;
        _defaultCommission = 10; // 10% default commission
    }

    /// @notice Sets the registry contract address
    /// @param registryContract The registry contract address
    function setRegistryContract(address registryContract) external onlyOwner {
        require(registryContract != address(0), "TLD: Invalid registry");
        _registryContract = registryContract;
    }

    /// @notice Registers a new TLD
    /// @param id The unique ID for the TLD
    /// @param name The TLD name
    /// @param tldOwner The owner of the TLD
    function registerTLD(
        uint256 id,
        string calldata name,
        address tldOwner
    ) external onlyRegistryOrOwner {
        require(id > 0, "TLD: Invalid ID");
        require(bytes(name).length > 0, "TLD: Empty name");
        require(tldOwner != address(0), "TLD: Invalid owner");
        require(!_tldById[id].exists, "TLD: Already exists");
        require(_tldIdByName[name] == 0, "TLD: Name taken");

        _tldById[id] = TLDInfo({
            id: id,
            name: name,
            price: 1 ether, // Default subdomain price
            commission: _defaultCommission,
            erc20Token: address(0),
            owner: tldOwner,
            exists: true
        });

        _tldIdByName[name] = id;
        _tldsByOwner[tldOwner].push(id);

        emit TLDRegistered(id, name, tldOwner);
    }

    /// @notice Sets the subdomain minting price for a TLD
    /// @param id The TLD ID
    /// @param price The new price (0 = free, 1982 = locked)
    function setTLDPrice(uint256 id, uint256 price) external {
        require(_tldById[id].exists, "TLD: Not found");
        require(_tldById[id].owner == msg.sender || owner() == msg.sender, "TLD: Not authorized");

        _tldById[id].price = price;
        emit TLDPriceSet(id, price);
    }

    /// @notice Sets the commission percentage for a TLD
    /// @param id The TLD ID
    /// @param commission The commission percentage (0-99)
    function setTLDCommission(uint256 id, uint256 commission) external onlyOwner {
        require(_tldById[id].exists, "TLD: Not found");
        require(commission < 100, "TLD: Invalid commission");

        _tldById[id].commission = commission;
        emit TLDCommissionSet(id, commission);
    }

    /// @notice Associates an ERC20 token with a TLD for payments
    /// @param id The TLD ID
    /// @param token The ERC20 token address (address(0) to use ETH)
    function setTLDToken(uint256 id, address token) external {
        require(_tldById[id].exists, "TLD: Not found");
        require(_tldById[id].owner == msg.sender, "TLD: Not owner");

        _tldById[id].erc20Token = token;
        emit ERC20TokenSet(id, token);
    }

    /// @notice Transfers TLD ownership
    /// @param id The TLD ID
    /// @param newOwner The new owner address
    function transferTLDOwnership(uint256 id, address newOwner) external {
        require(_tldById[id].exists, "TLD: Not found");
        require(_tldById[id].owner == msg.sender, "TLD: Not owner");
        require(newOwner != address(0), "TLD: Invalid new owner");

        address previousOwner = _tldById[id].owner;
        _tldById[id].owner = newOwner;

        // Update owner mappings
        _removeTLDFromOwner(previousOwner, id);
        _tldsByOwner[newOwner].push(id);

        emit TLDOwnershipTransferred(id, previousOwner, newOwner);
    }

    /// @notice Gets TLD information by ID
    /// @param id The TLD ID
    /// @return TLD information struct
    function getTLD(uint256 id) external view returns (TLDInfo memory) {
        require(_tldById[id].exists, "TLD: Not found");
        return _tldById[id];
    }

    /// @notice Gets TLD ID by name
    /// @param name The TLD name
    /// @return The TLD ID
    function getTLDId(string calldata name) external view returns (uint256) {
        uint256 id = _tldIdByName[name];
        require(id > 0, "TLD: Not found");
        return id;
    }

    /// @notice Gets TLD price by ID
    /// @param id The TLD ID
    /// @return The subdomain minting price
    function getTLDPrice(uint256 id) external view returns (uint256) {
        require(_tldById[id].exists, "TLD: Not found");
        return _tldById[id].price;
    }

    /// @notice Gets TLD commission by ID
    /// @param id The TLD ID
    /// @return The commission percentage
    function getTLDCommission(uint256 id) external view returns (uint256) {
        require(_tldById[id].exists, "TLD: Not found");
        return _tldById[id].commission;
    }

    /// @notice Gets TLD ERC20 token by ID
    /// @param id The TLD ID
    /// @return The associated ERC20 token address
    function getTLDToken(uint256 id) external view returns (address) {
        require(_tldById[id].exists, "TLD: Not found");
        return _tldById[id].erc20Token;
    }

    /// @notice Gets TLD owner by ID
    /// @param id The TLD ID
    /// @return The TLD owner address
    function getTLDOwner(uint256 id) external view returns (address) {
        require(_tldById[id].exists, "TLD: Not found");
        return _tldById[id].owner;
    }

    /// @notice Checks if a TLD exists
    /// @param id The TLD ID
    /// @return Whether the TLD exists
    function tldExists(uint256 id) external view returns (bool) {
        return _tldById[id].exists;
    }

    /// @notice Gets TLDs owned by an address
    /// @param owner The owner address
    /// @return Array of TLD IDs
    function getTLDsByOwner(address owner) external view returns (uint256[] memory) {
        return _tldsByOwner[owner];
    }

    /// @notice Sets the base TLD registration price
    /// @param price The new base price
    function setBaseTLDPrice(uint256 price) external onlyOwner {
        _baseTLDPrice = price;
    }

    /// @notice Gets the base TLD registration price
    /// @return The base TLD price
    function getBaseTLDPrice() external view returns (uint256) {
        return _baseTLDPrice;
    }

    /// @notice Sets the default commission percentage
    /// @param commission The new default commission
    function setDefaultCommission(uint256 commission) external onlyOwner {
        require(commission < 100, "TLD: Invalid commission");
        _defaultCommission = commission;
    }

    /// @notice Gets the default commission percentage
    /// @return The default commission
    function getDefaultCommission() external view returns (uint256) {
        return _defaultCommission;
    }

    /// @dev Removes a TLD from an owner's list
    /// @param owner The owner address
    /// @param id The TLD ID to remove
    function _removeTLDFromOwner(address owner, uint256 id) private {
        uint256[] storage tlds = _tldsByOwner[owner];
        for (uint256 i = 0; i < tlds.length; i++) {
            if (tlds[i] == id) {
                tlds[i] = tlds[tlds.length - 1];
                tlds.pop();
                break;
            }
        }
    }

    /// @notice Gets the registry contract address
    /// @return The registry contract address
    function getRegistryContract() external view returns (address) {
        return _registryContract;
    }

    /// @dev Modifier to restrict access to registry contract or owner
    modifier onlyRegistryOrOwner() {
        require(
            msg.sender == _registryContract || msg.sender == owner(),
            "TLD: Not authorized"
        );
        _;
    }

    /// @dev Authorization function for upgrades
    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}
}
