const { ethers, upgrades } = require("hardhat");

async function main() {
  console.log("Starting ODude contracts deployment...");
  
  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("Deploying contracts with account:", deployer.address);
  console.log("Account balance:", ethers.formatEther(await ethers.provider.getBalance(deployer.address)));

  // Deploy TLD contract
  console.log("\n1. Deploying TLD contract...");
  const TLD = await ethers.getContractFactory("TLD");
  const tld = await upgrades.deployProxy(TLD, [deployer.address], {
    initializer: "initialize",
    kind: "uups"
  });
  await tld.waitForDeployment();
  const tldAddress = await tld.getAddress();
  console.log("TLD deployed to:", tldAddress);

  // Deploy Resolver contract
  console.log("\n2. Deploying Resolver contract...");
  const Resolver = await ethers.getContractFactory("Resolver");
  const resolver = await upgrades.deployProxy(Resolver, [deployer.address], {
    initializer: "initialize",
    kind: "uups"
  });
  await resolver.waitForDeployment();
  const resolverAddress = await resolver.getAddress();
  console.log("Resolver deployed to:", resolverAddress);

  // Deploy Registry contract
  console.log("\n3. Deploying Registry contract...");
  const Registry = await ethers.getContractFactory("Registry");
  const registry = await upgrades.deployProxy(Registry, [deployer.address], {
    initializer: "initialize",
    kind: "uups"
  });
  await registry.waitForDeployment();
  const registryAddress = await registry.getAddress();
  console.log("Registry deployed to:", registryAddress);

  // Configure contract relationships
  console.log("\n4. Configuring contract relationships...");
  
  // Set TLD and Resolver addresses in Registry
  console.log("Setting TLD and Resolver contracts in Registry...");
  const setContractsTx = await registry.setContracts(tldAddress, resolverAddress);
  await setContractsTx.wait();
  console.log("✓ Registry configured with TLD and Resolver addresses");

  // Set Registry address in Resolver
  console.log("Setting Registry contract in Resolver...");
  const setRegistryTx = await resolver.setRegistryContract(registryAddress);
  await setRegistryTx.wait();
  console.log("✓ Resolver configured with Registry address");

  // Set Registry address in TLD
  console.log("Setting Registry contract in TLD...");
  const setRegistryInTLDTx = await tld.setRegistryContract(registryAddress);
  await setRegistryInTLDTx.wait();
  console.log("✓ TLD configured with Registry address");

  // Set initial configuration
  console.log("\n5. Setting initial configuration...");
  
  // Set base TLD price (1 ETH)
  const basePriceTx = await tld.setBaseTLDPrice(ethers.parseEther("1.0"));
  await basePriceTx.wait();
  console.log("✓ Base TLD price set to 1 ETH");

  // Set default commission (10%)
  const commissionTx = await tld.setDefaultCommission(10);
  await commissionTx.wait();
  console.log("✓ Default commission set to 10%");

  // Verify deployments
  console.log("\n6. Verifying deployments...");
  
  // Check TLD contract
  const tldOwner = await tld.owner();
  const baseTLDPrice = await tld.getBaseTLDPrice();
  const defaultCommission = await tld.getDefaultCommission();
  console.log("TLD Contract:");
  console.log("  - Owner:", tldOwner);
  console.log("  - Base TLD Price:", ethers.formatEther(baseTLDPrice), "ETH");
  console.log("  - Default Commission:", defaultCommission.toString() + "%");

  // Check Resolver contract
  const resolverOwner = await resolver.owner();
  const registryInResolver = await resolver.getRegistryContract();
  console.log("Resolver Contract:");
  console.log("  - Owner:", resolverOwner);
  console.log("  - Registry Contract:", registryInResolver);

  // Check Registry contract
  const registryOwner = await registry.owner();
  const tldInRegistry = await registry.getTLDContract();
  const resolverInRegistry = await registry.getResolverContract();
  console.log("Registry Contract:");
  console.log("  - Owner:", registryOwner);
  console.log("  - TLD Contract:", tldInRegistry);
  console.log("  - Resolver Contract:", resolverInRegistry);

  // Save deployment addresses to file
  const deploymentInfo = {
    network: hre.network.name,
    deployer: deployer.address,
    timestamp: new Date().toISOString(),
    contracts: {
      TLD: {
        address: tldAddress,
        implementation: await upgrades.erc1967.getImplementationAddress(tldAddress)
      },
      Resolver: {
        address: resolverAddress,
        implementation: await upgrades.erc1967.getImplementationAddress(resolverAddress)
      },
      Registry: {
        address: registryAddress,
        implementation: await upgrades.erc1967.getImplementationAddress(registryAddress)
      }
    },
    configuration: {
      baseTLDPrice: ethers.formatEther(baseTLDPrice),
      defaultCommission: defaultCommission.toString()
    }
  };

  const fs = require('fs');
  const path = require('path');
  
  // Create deployments directory if it doesn't exist
  const deploymentsDir = path.join(__dirname, '..', 'deployments');
  if (!fs.existsSync(deploymentsDir)) {
    fs.mkdirSync(deploymentsDir, { recursive: true });
  }

  // Save deployment info
  const deploymentFile = path.join(deploymentsDir, `${hre.network.name}-deployment.json`);
  fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
  console.log(`\n7. Deployment info saved to: ${deploymentFile}`);

  console.log("\n🎉 Deployment completed successfully!");
  console.log("\nContract Addresses:");
  console.log("==================");
  console.log("TLD:      ", tldAddress);
  console.log("Resolver: ", resolverAddress);
  console.log("Registry: ", registryAddress);
  
  console.log("\nNext Steps:");
  console.log("===========");
  console.log("1. Verify contracts on block explorer (if on testnet/mainnet)");
  console.log("2. Run tests: npx hardhat test");
  console.log("3. Try minting a TLD: Use the Registry contract's claim function");
  
  return {
    tld: tldAddress,
    resolver: resolverAddress,
    registry: registryAddress
  };
}

// Handle script execution
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error("Deployment failed:", error);
      process.exit(1);
    });
}

module.exports = main;
