// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";

/// @title Resolver - Name Resolution Service Contract
/// @notice Manages address-to-name and name-to-address resolution with reverse mapping
/// @custom:security-contact <EMAIL>
contract Resolver is 
    Initializable,
    OwnableUpgradeable,
    UUPSUpgradeable,
    ReentrancyGuardUpgradeable
{
    /// @dev Resolution record structure
    struct ResolutionRecord {
        address resolvedAddress;
        string name;
        uint256 tokenId;
        bool exists;
    }

    /// @dev Reverse mapping structure
    struct ReverseRecord {
        string primaryName;
        uint256 primaryTokenId;
        bool exists;
    }

    // Name to address resolution
    mapping(string => ResolutionRecord) private _nameToRecord;
    
    // Address to primary name reverse mapping
    mapping(address => ReverseRecord) private _addressToReverse;
    
    // Token ID to name mapping
    mapping(uint256 => string) private _tokenIdToName;
    
    // Registry contract address (authorized to update records)
    address private _registryContract;

    // Events
    event NameResolved(string indexed name, address indexed resolvedAddress, uint256 indexed tokenId);
    event ReverseSet(address indexed addr, string indexed name, uint256 indexed tokenId);
    event ReverseRemoved(address indexed addr, string indexed previousName);
    event RegistryContractSet(address indexed previousRegistry, address indexed newRegistry);

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    /// @notice Initializes the Resolver contract
    /// @param initialOwner The initial owner of the contract
    function initialize(address initialOwner) public initializer {
        __Ownable_init(initialOwner);
        __UUPSUpgradeable_init();
        __ReentrancyGuard_init();
    }

    /// @notice Sets the registry contract address
    /// @param registryContract The registry contract address
    function setRegistryContract(address registryContract) external onlyOwner {
        require(registryContract != address(0), "Resolver: Invalid registry");
        
        address previousRegistry = _registryContract;
        _registryContract = registryContract;
        
        emit RegistryContractSet(previousRegistry, registryContract);
    }

    /// @notice Sets name resolution record (only callable by registry)
    /// @param name The domain name
    /// @param resolvedAddress The address to resolve to
    /// @param tokenId The associated token ID
    function setNameRecord(
        string calldata name,
        address resolvedAddress,
        uint256 tokenId
    ) external onlyRegistryOrOwner {
        require(bytes(name).length > 0, "Resolver: Empty name");
        require(resolvedAddress != address(0), "Resolver: Invalid address");
        require(tokenId > 0, "Resolver: Invalid token ID");

        _nameToRecord[name] = ResolutionRecord({
            resolvedAddress: resolvedAddress,
            name: name,
            tokenId: tokenId,
            exists: true
        });

        _tokenIdToName[tokenId] = name;

        emit NameResolved(name, resolvedAddress, tokenId);
    }

    /// @notice Removes a name resolution record (only callable by registry)
    /// @param name The domain name to remove
    function removeNameRecord(string calldata name) external onlyRegistryOrOwner {
        require(_nameToRecord[name].exists, "Resolver: Record not found");
        
        uint256 tokenId = _nameToRecord[name].tokenId;
        delete _nameToRecord[name];
        delete _tokenIdToName[tokenId];
    }

    /// @notice Sets reverse mapping for an address (only callable by registry)
    /// @param addr The address to set reverse mapping for
    /// @param name The primary name for the address
    /// @param tokenId The associated token ID
    function setReverse(
        address addr,
        string calldata name,
        uint256 tokenId
    ) external onlyRegistryOrOwner {
        require(addr != address(0), "Resolver: Invalid address");
        require(bytes(name).length > 0, "Resolver: Empty name");
        require(tokenId > 0, "Resolver: Invalid token ID");
        require(_nameToRecord[name].exists, "Resolver: Name not registered");

        _addressToReverse[addr] = ReverseRecord({
            primaryName: name,
            primaryTokenId: tokenId,
            exists: true
        });

        emit ReverseSet(addr, name, tokenId);
    }

    /// @notice Removes reverse mapping for an address (only callable by registry)
    /// @param addr The address to remove reverse mapping for
    function removeReverse(address addr) external onlyRegistryOrOwner {
        require(_addressToReverse[addr].exists, "Resolver: Reverse not found");
        
        string memory previousName = _addressToReverse[addr].primaryName;
        delete _addressToReverse[addr];
        
        emit ReverseRemoved(addr, previousName);
    }

    /// @notice Resolves a name to an address
    /// @param name The domain name to resolve
    /// @return The resolved address
    function resolve(string calldata name) external view returns (address) {
        require(_nameToRecord[name].exists, "Resolver: Name not found");
        return _nameToRecord[name].resolvedAddress;
    }

    /// @notice Gets the primary name for an address (reverse lookup)
    /// @param addr The address to lookup
    /// @return The primary name for the address
    function reverse(address addr) external view returns (string memory) {
        if (!_addressToReverse[addr].exists) {
            return "";
        }
        return _addressToReverse[addr].primaryName;
    }

    /// @notice Gets the token ID associated with a name
    /// @param name The domain name
    /// @return The associated token ID
    function getTokenId(string calldata name) external view returns (uint256) {
        require(_nameToRecord[name].exists, "Resolver: Name not found");
        return _nameToRecord[name].tokenId;
    }

    /// @notice Gets the name associated with a token ID
    /// @param tokenId The token ID
    /// @return The associated name
    function getName(uint256 tokenId) external view returns (string memory) {
        return _tokenIdToName[tokenId];
    }

    /// @notice Checks if a name is registered
    /// @param name The domain name to check
    /// @return Whether the name is registered
    function nameExists(string calldata name) external view returns (bool) {
        return _nameToRecord[name].exists;
    }

    /// @notice Checks if an address has a reverse mapping
    /// @param addr The address to check
    /// @return Whether the address has a reverse mapping
    function hasReverse(address addr) external view returns (bool) {
        return _addressToReverse[addr].exists;
    }

    /// @notice Gets complete resolution record for a name
    /// @param name The domain name
    /// @return The complete resolution record
    function getResolutionRecord(string calldata name) external view returns (ResolutionRecord memory) {
        require(_nameToRecord[name].exists, "Resolver: Name not found");
        return _nameToRecord[name];
    }

    /// @notice Gets complete reverse record for an address
    /// @param addr The address
    /// @return The complete reverse record
    function getReverseRecord(address addr) external view returns (ReverseRecord memory) {
        require(_addressToReverse[addr].exists, "Resolver: Reverse not found");
        return _addressToReverse[addr];
    }

    /// @notice Gets the registry contract address
    /// @return The registry contract address
    function getRegistryContract() external view returns (address) {
        return _registryContract;
    }

    /// @dev Modifier to restrict access to registry contract or owner
    modifier onlyRegistryOrOwner() {
        require(
            msg.sender == _registryContract || msg.sender == owner(),
            "Resolver: Not authorized"
        );
        _;
    }

    /// @dev Authorization function for upgrades
    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}
}
