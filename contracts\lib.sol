// Enable optimizer
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

library ODudeUtils {

        function toLower(string memory _str) internal pure returns (string memory) {
        bytes memory strBytes = bytes(_str);
        uint len = strBytes.length;
        bytes memory lowercased = new bytes(len);

        for (uint i = 0; i < len; i++) {
            // Convert uppercase letters to lowercase
            if ((uint8(strBytes[i]) >= 65) && (uint8(strBytes[i]) <= 90)) {
                lowercased[i] = bytes1(uint8(strBytes[i]) + 32);
            } else {
                lowercased[i] = strBytes[i];
            }
        }

        return string(lowercased);
    }

}