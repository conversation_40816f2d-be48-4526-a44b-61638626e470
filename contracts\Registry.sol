// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

// OpenZeppelin upgradeable contracts for ERC721 functionality
import "@openzeppelin/contracts-upgradeable/token/ERC721/ERC721Upgradeable.sol";
import "@openzeppelin/contracts-upgradeable/token/ERC721/extensions/ERC721EnumerableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/token/ERC721/extensions/ERC721URIStorageUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/token/ERC721/extensions/ERC721PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";

// Custom helpers and interfaces
import "./helper.sol";
import "./lib.sol";
import "./TLD.sol";
import "./Resolver.sol";

/// @title Registry - Main NFT Registry Contract
/// @notice Handles NFT minting, payments, metadata storage, and orchestrates TLD and Resolver interactions
/// @custom:security-contact <EMAIL>
contract Registry is
    Initializable,
    ERC721Upgradeable,
    ERC721EnumerableUpgradeable,
    ERC721URIStorageUpgradeable,
    ERC721PausableUpgradeable,
    OwnableUpgradeable,
    UUPSUpgradeable,
    ReentrancyGuardUpgradeable
{
    // Attach custom utility methods for string
    using ODudeUtils for string;

    /// @dev NFT metadata structure
    struct NFTMetadata {
        uint256 id;
        string name;
        bool isTLD;
        uint256 parentTLD; // 0 if TLD, parent TLD ID if subdomain
    }

    // Contract references
    TLD private _tldContract;
    Resolver private _resolverContract;

    // NFT metadata mapping
    mapping(uint256 => NFTMetadata) private _nftMetadata;
    mapping(string => uint256) private _nameToTokenId;

    // Events
    event EthPayment(address indexed payer, uint256 tldOwnerAmount, uint256 platformAmount);
    event ERC20Payment(address indexed payer, address indexed token, uint256 tldOwnerAmount, uint256 platformAmount);
    event TLDMinted(uint256 indexed tokenId, string indexed name, address indexed owner);
    event SubdomainMinted(uint256 indexed tokenId, string indexed name, uint256 indexed parentTLD, address owner);
    event ContractsSet(address indexed tldContract, address indexed resolverContract);

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    /// @notice Initializes the Registry contract
    /// @param initialOwner The initial owner of the contract
    function initialize(address initialOwner) public initializer {
        __ERC721_init("ODude", "ODUDE");
        __ERC721Enumerable_init();
        __ERC721URIStorage_init();
        __ERC721Pausable_init();
        __Ownable_init(initialOwner);
        __UUPSUpgradeable_init();
        __ReentrancyGuard_init();
    }

    /// @notice Sets the TLD and Resolver contract addresses
    /// @param tldContract The TLD contract address
    /// @param resolverContract The Resolver contract address
    function setContracts(address tldContract, address resolverContract) external onlyOwner {
        require(tldContract != address(0), "Registry: Invalid TLD contract");
        require(resolverContract != address(0), "Registry: Invalid Resolver contract");
        
        _tldContract = TLD(tldContract);
        _resolverContract = Resolver(resolverContract);
        
        emit ContractsSet(tldContract, resolverContract);
    }

    /// @notice Pauses all token transfers
    function pause() public onlyOwner {
        _pause();
    }

    /// @notice Resumes token transfers
    function unpause() public onlyOwner {
        _unpause();
    }

    /// @notice Claims a TLD or subdomain NFT, handling ETH/ERC20 payments and commissions
    /// @param tokenId The token ID to mint
    /// @param name The domain name
    /// @param tokenUri The token URI
    /// @param to The address to mint to
    function claim(
        uint256 tokenId,
        string calldata name,
        string memory tokenUri,
        address to
    ) external payable nonReentrant {
        require(tokenId > 0, "Registry: Invalid token ID");
        require(bytes(name).length > 0, "Registry: Empty name");
        require(to != address(0), "Registry: Invalid recipient");
        require(!_exists(tokenId), "Registry: Token exists");
        require(_nameToTokenId[ODudeUtils.toLower(name)] == 0, "Registry: Name taken");

        string memory lowerName = ODudeUtils.toLower(name);
        string memory primary = _subdomain(lowerName, 2);
        
        if (keccak256(bytes(primary)) != keccak256(bytes(""))) {
            // It's a subdomain
            _claimSubdomain(tokenId, lowerName, tokenUri, to, primary);
        } else {
            // It's a TLD
            _claimTLD(tokenId, lowerName, tokenUri, to);
        }
    }

    /// @notice Allows NFT owner to update their token URI
    /// @param tokenId The token ID
    /// @param newTokenURI The new token URI
    function setTokenURI(uint256 tokenId, string memory newTokenURI) external payable {
        require(ownerOf(tokenId) == msg.sender, "Registry: Not owner");
        
        // Check if subdomain and if parent TLD allows URI updates
        NFTMetadata memory metadata = _nftMetadata[tokenId];
        if (!metadata.isTLD && metadata.parentTLD > 0) {
            uint256 allowance = _tldContract.getTLDPrice(metadata.parentTLD);
            require(allowance != 1982, "Registry: URI locked");
        }
        
        _setTokenURI(tokenId, newTokenURI);
        
        // Forward any payment to owner
        if (msg.value > 0) {
            payable(owner()).transfer(msg.value);
        }
    }

    /// @notice Sets reverse mapping for caller
    /// @param tokenId The token ID to set as primary
    function setReverse(uint256 tokenId) external payable {
        require(ownerOf(tokenId) == msg.sender, "Registry: Not owner");
        
        NFTMetadata memory metadata = _nftMetadata[tokenId];
        _resolverContract.setReverse(msg.sender, metadata.name, tokenId);
        
        // Forward any payment to owner
        if (msg.value > 0) {
            payable(owner()).transfer(msg.value);
        }
    }

    /// @notice Gets the name associated with a token ID
    /// @param tokenId The token ID
    /// @return The domain name
    function nameOf(uint256 tokenId) external view returns (string memory) {
        require(_exists(tokenId), "Registry: Token not found");
        return _nftMetadata[tokenId].name;
    }

    /// @notice Gets token ID by name
    /// @param name The domain name
    /// @return The token ID
    function getTokenId(string calldata name) external view returns (uint256) {
        uint256 tokenId = _nameToTokenId[ODudeUtils.toLower(name)];
        require(tokenId > 0, "Registry: Name not found");
        return tokenId;
    }

    /// @notice Gets the owner address by name
    /// @param name The domain name
    /// @return The owner address
    function getOwnerByName(string calldata name) external view returns (address) {
        uint256 tokenId = _nameToTokenId[ODudeUtils.toLower(name)];
        require(tokenId > 0, "Registry: Name not found");
        return ownerOf(tokenId);
    }

    /// @notice Gets NFT metadata
    /// @param tokenId The token ID
    /// @return The NFT metadata
    function getNFTMetadata(uint256 tokenId) external view returns (NFTMetadata memory) {
        require(_exists(tokenId), "Registry: Token not found");
        return _nftMetadata[tokenId];
    }

    /// @notice Gets contract balance
    /// @return The ETH balance
    function getBalance() external view returns (uint256) {
        return address(this).balance;
    }

    /// @notice Withdraws ETH from contract
    function withdraw() external onlyOwner {
        payable(owner()).transfer(address(this).balance);
    }

    /// @notice Withdraws ERC20 tokens from contract
    /// @param tokenAddress The ERC20 token address
    /// @param amount The amount to withdraw
    function withdrawERC20(address tokenAddress, uint256 amount) external onlyOwner {
        require(tokenAddress != address(0), "Registry: Invalid token");
        require(amount > 0, "Registry: Invalid amount");
        
        IERC20 token = IERC20(tokenAddress);
        require(token.balanceOf(address(this)) >= amount, "Registry: Insufficient balance");
        require(token.transfer(owner(), amount), "Registry: Transfer failed");
    }

    /// @notice Gets TLD contract address
    /// @return The TLD contract address
    function getTLDContract() external view returns (address) {
        return address(_tldContract);
    }

    /// @notice Gets Resolver contract address
    /// @return The Resolver contract address
    function getResolverContract() external view returns (address) {
        return address(_resolverContract);
    }

    /// @dev Internal function to claim a TLD
    function _claimTLD(
        uint256 tokenId,
        string memory name,
        string memory tokenUri,
        address to
    ) private {
        uint256 price = _tldContract.getBaseTLDPrice();
        require(msg.value >= price, "Registry: Insufficient payment");
        
        // Register TLD in TLD contract
        _tldContract.registerTLD(tokenId, name, to);
        
        // Mint NFT
        _safeMint(to, tokenId);
        _setTokenURI(tokenId, tokenUri);
        
        // Set metadata
        _nftMetadata[tokenId] = NFTMetadata({
            id: tokenId,
            name: name,
            isTLD: true,
            parentTLD: 0
        });
        
        _nameToTokenId[name] = tokenId;
        
        // Set resolver record
        _resolverContract.setNameRecord(name, to, tokenId);
        
        // Forward payment to owner
        payable(owner()).transfer(msg.value);
        
        emit TLDMinted(tokenId, name, to);
        emit EthPayment(msg.sender, 0, msg.value);
    }

    /// @dev Internal function to claim a subdomain
    function _claimSubdomain(
        uint256 tokenId,
        string memory name,
        string memory tokenUri,
        address to,
        string memory parentTLDName
    ) private {
        uint256 parentTLDId = _tldContract.getTLDId(parentTLDName);
        uint256 price = _tldContract.getTLDPrice(parentTLDId);

        // Check if subdomain minting is allowed
        if (price == 0 || price == 1982) {
            // Free or locked - only TLD owner can mint
            require(msg.sender == _tldContract.getTLDOwner(parentTLDId), "Registry: Not TLD owner");
            if (msg.value > 0) {
                payable(owner()).transfer(msg.value);
            }
        } else {
            // Paid subdomain
            address erc20Token = _tldContract.getTLDToken(parentTLDId);
            uint256 commission = _tldContract.getTLDCommission(parentTLDId);

            uint256 tldOwnerAmount = (price * commission) / 100;
            uint256 platformAmount = price - tldOwnerAmount;

            if (erc20Token != address(0)) {
                // ERC20 payment
                _processERC20Payment(erc20Token, price, tldOwnerAmount, platformAmount, parentTLDId);
            } else {
                // ETH payment
                require(msg.value >= price, "Registry: Insufficient payment");

                address tldOwner = _tldContract.getTLDOwner(parentTLDId);
                payable(tldOwner).transfer(tldOwnerAmount);
                payable(owner()).transfer(platformAmount);

                emit EthPayment(msg.sender, tldOwnerAmount, platformAmount);
            }
        }

        // Mint NFT
        _safeMint(to, tokenId);
        _setTokenURI(tokenId, tokenUri);

        // Set metadata
        _nftMetadata[tokenId] = NFTMetadata({
            id: tokenId,
            name: name,
            isTLD: false,
            parentTLD: parentTLDId
        });

        _nameToTokenId[name] = tokenId;

        // Set resolver record
        _resolverContract.setNameRecord(name, to, tokenId);

        emit SubdomainMinted(tokenId, name, parentTLDId, to);
    }

    /// @dev Processes ERC20 payment for subdomain minting
    function _processERC20Payment(
        address token,
        uint256 totalAmount,
        uint256 tldOwnerAmount,
        uint256 platformAmount,
        uint256 parentTLDId
    ) private {
        IERC20 erc20 = IERC20(token);

        // Check balance and allowance
        require(erc20.balanceOf(msg.sender) >= totalAmount, "Registry: Insufficient balance");
        require(erc20.allowance(msg.sender, address(this)) >= totalAmount, "Registry: Insufficient allowance");

        // Transfer tokens to contract
        require(erc20.transferFrom(msg.sender, address(this), totalAmount), "Registry: Transfer failed");

        // Distribute tokens
        address tldOwner = _tldContract.getTLDOwner(parentTLDId);
        require(erc20.transfer(tldOwner, tldOwnerAmount), "Registry: TLD owner payment failed");
        require(erc20.transfer(owner(), platformAmount), "Registry: Platform payment failed");

        emit ERC20Payment(msg.sender, token, tldOwnerAmount, platformAmount);
    }

    /// @dev Splits a name at "@" and returns the specified part
    function _subdomain(string memory name, uint256 pos) private pure returns (string memory) {
        (string memory left, string memory right) = Helper.split(name, "@");
        return pos == 1 ? left : right;
    }

    /// @dev Checks if a token exists
    function _exists(uint256 tokenId) private view returns (bool) {
        return _nftMetadata[tokenId].id != 0;
    }

    /// @dev Required override for _update function
    function _update(
        address to,
        uint256 tokenId,
        address auth
    )
        internal
        override(
            ERC721Upgradeable,
            ERC721EnumerableUpgradeable,
            ERC721PausableUpgradeable
        )
        returns (address)
    {
        return super._update(to, tokenId, auth);
    }

    /// @dev Required override for _increaseBalance function
    function _increaseBalance(
        address account,
        uint128 value
    ) internal override(ERC721Upgradeable, ERC721EnumerableUpgradeable) {
        super._increaseBalance(account, value);
    }

    /// @dev Required override for tokenURI function
    function tokenURI(
        uint256 tokenId
    )
        public
        view
        override(ERC721Upgradeable, ERC721URIStorageUpgradeable)
        returns (string memory)
    {
        return super.tokenURI(tokenId);
    }

    /// @dev Required override for supportsInterface function
    function supportsInterface(
        bytes4 interfaceId
    )
        public
        view
        override(
            ERC721Upgradeable,
            ERC721EnumerableUpgradeable,
            ERC721URIStorageUpgradeable
        )
        returns (bool)
    {
        return super.supportsInterface(interfaceId);
    }

    /// @dev Authorization function for upgrades
    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    /// @notice Accepts plain ETH transfers
    receive() external payable {}

    /// @notice Accepts unknown calls with data or ETH
    fallback() external payable {}
}
