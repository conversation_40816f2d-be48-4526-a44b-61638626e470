I have the following Solidity files in my project:
- odude.sol (main smart contract, upgradeable)
- helper.sol (helper functions)
- lib.sol (library functions)

I want to convert this into a **modular, upgradeable Hardhat project** with OpenZeppelin upgrades. 
The AI should generate everything automatically, including deploy scripts, test scripts, and documentation.

Requirements:

1. **Contracts**:
   - Split odude.sol into 3 upgradeable contracts: Registry.sol, TLD.sol, Resolver.sol.
   - All contracts should be **upgradeable** using OpenZeppelin's UUPS pattern.
   - Include proper imports for OpenZeppelin upgradeable contracts.
   - Maintain all functionality, mappings, structs, and events.
   - All helper.sol and lib.sol functions should be used appropriately in new files.

2. **Deployment**:
   - Create `scripts/deploy.js` to deploy all 3 contracts on Hardhat localhost.
   - After deployment, deploy script should **wire contracts to each other** where needed (Registry → TLD → Resolver).
   - Use OpenZeppelin `deployProxy` and ensure addresses are printed after deployment.

3. **Testing**:
   - Create a test script `test/test.js` using Hardhat and ethers.js.
   - Test should:
     - Deploy contracts
     - Mint a sample NFT / TLD
     - Set and get a few fields
     - Claim a sample subdomain / name
     - Test ERC20 payment (mock token)
     - Confirm reverse mapping works

4. **Project Setup**:
   - Hardhat project with `package.json` including:
     - `hardhat`
     - `@nomicfoundation/hardhat-toolbox`
     - `@openzeppelin/contracts-upgradeable`
     - `@openzeppelin/hardhat-upgrades`
   - `hardhat.config.js` configured for localhost and Solidity ^0.8.20

5. **Documentation**:
   - Generate `INSTRUCTIONS.md` with:
     - How to install dependencies
     - How to compile contracts
     - How to deploy locally
     - How to run tests
     - How to upgrade contracts later

6. **Coding Standards**:
   - All functions should have proper `onlyOwner`, `external`, `public`, `internal` visibility.
   - Include proper require/error messages.
   - Use proper events for payments and state changes.
   - Follow OpenZeppelin upgradeable contract best practices.

7. **Output**:
   - Return **all files** with content ready to copy to a Hardhat project:
     - contracts/*.sol
     - scripts/deploy.js
     - test/test.js
     - INSTRUCTIONS.md
