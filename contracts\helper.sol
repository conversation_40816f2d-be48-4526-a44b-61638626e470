// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

library Helper {
    /**
     * @dev Splits a string into two parts based on the first occurrence of a delimiter.
     * @param str The string to split.
     * @param delimiter The delimiter to split the string on.
     * @return part1 The part of the string before the delimiter.
     * @return part2 The part of the string after the delimiter.
     */
    function split(string memory str, string memory delimiter)
        internal
        pure
        returns (string memory part1, string memory part2)
    {
        bytes memory strBytes = bytes(str);
        bytes memory delimiterBytes = bytes(delimiter);
        uint256 delimiterIndex = 0;

        for (uint256 i = 0; i < strBytes.length; i++) {
            bool matchFound = true;
            for (uint256 j = 0; j < delimiterBytes.length; j++) {
                if (i + j >= strBytes.length || strBytes[i + j] != delimiterBytes[j]) {
                    matchFound = false;
                    break;
                }
            }
            if (matchFound) {
                delimiterIndex = i;
                break;
            }
        }

        if (delimiterIndex == 0) {
            return (str, "");
        }

        bytes memory part1Bytes = new bytes(delimiterIndex);
        for (uint256 i = 0; i < delimiterIndex; i++) {
            part1Bytes[i] = strBytes[i];
        }

        bytes memory part2Bytes = new bytes(strBytes.length - delimiterIndex - delimiterBytes.length);
        for (uint256 i = delimiterIndex + delimiterBytes.length; i < strBytes.length; i++) {
            part2Bytes[i - delimiterIndex - delimiterBytes.length] = strBytes[i];
        }

        return (string(part1Bytes), string(part2Bytes));
    }
}